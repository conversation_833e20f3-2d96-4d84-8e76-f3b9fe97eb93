/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import useStore from '+store';

import CardStatusControlModal from '../CardStatusControlModal';

const user = userEvent;
const MockedCardStatusControlModal = props => {
  return (
    <MockIndex>
      <CardStatusControlModal {...props} />
    </MockIndex>
  );
};

const mockOperation = vi.fn();

const props = {
  close: vi.fn(),
  type: 'suspend',
  operation: mockOperation
};

beforeEach(() => {
  useStore.setState({
    profile: {
      email: '<EMAIL>'
    }
  });
});
describe('Card Status Control Modal ', () => {
  test('render suspend card status control modal', () => {
    render(<MockedCardStatusControlModal {...props} />);

    const heading = screen.getByText('Suspend Card');
    const description = screen.getByText(
      'Suspending a card makes its balance blocked thus transactions cannot be carried out on such card.'
    );
    const suspendReason = screen.getByText('Why do you want to suspend this card?');

    expect(heading).toBeInTheDocument();
    expect(description).toBeInTheDocument();
    expect(suspendReason).toBeInTheDocument();
  });

  test('To display Confirm Suspend screen when user clicks on Suspend Proceed button', async () => {
    render(<MockedCardStatusControlModal {...props} />);
    const selectReason = screen.getByTestId('select-reason');

    await user.selectOptions(selectReason, 'fraud');

    const proceedButton = screen.getByText('Proceed');

    await user.click(proceedButton);

    await screen.findByText('Confirm suspension');
    await screen.findByText('Yes, Suspend');

    const confirmSuspendButton = await screen.findByText('Yes, Suspend');
    fireEvent.click(confirmSuspendButton);
    expect(mockOperation).toHaveBeenCalled();
  });

  test('To display input for other if the selected reason is Other', async () => {
    render(<MockedCardStatusControlModal {...props} />);

    const selectReason = screen.getByTestId('select-reason');
    fireEvent.change(selectReason, { target: { value: 'other' } });

    await waitFor(async () => expect(await screen.findByTestId('others')).toBeInTheDocument());
  });

  test('render active card status control modal', () => {
    props.type = 'activate';
    render(<MockedCardStatusControlModal {...props} />);

    const heading = screen.getByText('Confirm reactivation');
    const description = screen.getByText(
      'This card was suspended due to Fraudulent activities. Please confirm that you want to reactivate this card.'
    );

    expect(heading).toBeInTheDocument();
    expect(description).toBeInTheDocument();
  });

  test('render terminate card status control modal', () => {
    props.type = 'terminate';
    render(<MockedCardStatusControlModal {...props} />);

    const heading = screen.getByText('Terminate Card');
    const description = screen.getByText(
      'Terminating a card makes its balance blocked thus transactions cannot be carried out on such card.'
    );

    expect(heading).toBeInTheDocument();
    expect(description).toBeInTheDocument();
  });

  test('To display Confirm Terminate screen when user clicks on Terminate Proceed button', async () => {
    render(<MockedCardStatusControlModal {...props} />);
    const selectReason = screen.getByTestId('select-reason');

    await user.selectOptions(selectReason, 'fraud');

    const proceedButton = screen.getByText('Proceed');

    await user.click(proceedButton);

    await screen.findByText('Confirm termination');
    await screen.findByText('Yes, Terminate');

    const confirmSuspendButton = await screen.findByText('Yes, Terminate');
    fireEvent.click(confirmSuspendButton);
    expect(mockOperation).toHaveBeenCalled();
  });

  test('To Reactivate works', async () => {
    props.type = 'activate';
    render(<MockedCardStatusControlModal {...props} />);

    const confirmSuspendButton = await screen.findByText('Yes, Reactivate');
    fireEvent.click(confirmSuspendButton);
    expect(mockOperation).toHaveBeenCalled();
  });
});
