import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { ErrorMessage, Field, Formik, useFormikContext } from 'formik';

import Modal from '+containers/Dashboard/Shared/Modal';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import { AccessRequestMerchantResponse, CategoryType, CurrencyType, IModalProps, ModifyAccessPayloadType } from '+types';
import { history, logError } from '+utils';

import {
  declineReasonOptions,
  generateMerchantPCIDSSLimitDetailsFrom,
  generateMerchantRequestDetailsFrom,
  generateMerchantRiskLevelFundingLimitDetailsFrom,
  generateMerchantRiskLevelSpendingLimitDetailsFrom
} from '../../helpers/accessRequestReviewModalHelpers';
import { ConfirmRequestApproval, RequestingMerchantOverview } from './RequestingMerchantOverview';

type ModalStepType = 'request_approval' | 'request_decline' | 'confirm_approval';

type FormValueType = {
  declineReason: string;
  otherDeclineReason: string;
  confirmAction: boolean;
};

const apiRequest = new APIRequest();

const AccessRequestReviewModal = ({
  action,
  merchantData,
  onClose,
  feature,
  currency
}: {
  action: Extract<ModalStepType, 'request_approval' | 'request_decline'> | null;
  merchantData?: AccessRequestMerchantResponse;
  onClose: () => void;
  feature: CategoryType;
  currency: CurrencyType;
}) => {
  const queryClient = useQueryClient();
  const { feedbackInit, closeFeedback } = useFeedbackHandler();
  const [step, setStep] = useState<ModalStepType | null>(action);
  const isCustomerCardCategory = feature === 'issued-cards';
  const cardCategory = isCustomerCardCategory ? 'Issued' : 'Reserved';
  const merchantPCIDSSLimitDetails = generateMerchantPCIDSSLimitDetailsFrom(merchantData);
  const merchantSpendingLimitDetails = generateMerchantRiskLevelSpendingLimitDetailsFrom(currency, merchantData);
  const merchantFundingLimitDetails = generateMerchantRiskLevelFundingLimitDetailsFrom(currency, merchantData);
  const merchantRequestDetails = generateMerchantRequestDetailsFrom(merchantData);

  const { mutateAsync: mutateModifyAccess } = useMutation((payload: ModifyAccessPayloadType) => apiRequest.modifyAccess(payload), {
    onSuccess: data => {
      feedbackInit({ message: data?.message, type: 'success' });
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG_SETTING`]);
      queryClient.invalidateQueries(['REQUESTING_MERCHANT_DETAILS']);
      queryClient.invalidateQueries(['ISSUING_MERCHANT_PLANS']);
      queryClient.invalidateQueries(['MERCHANT_DETAILS']);
      queryClient.invalidateQueries(['ISSUING_MERCHANTS']);
      queryClient.invalidateQueries(['REQUESTING_MERCHANTS']);
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: error.response?.data?.message || 'Action was unsuccessful',
        type: 'danger',
        componentLevel: true
      });
    },
    onSettled: () => {
      setTimeout(() => {
        closeFeedback();
      }, 5000);
    }
  });

  const gotoPreviousStep = () => {
    if (step === 'confirm_approval') setStep('request_approval');
    else onClose();
  };

  const validateForm = (values: FormValueType) => {
    let errors: Record<string, string> = {};
    if (step === 'request_decline') {
      if (!values.declineReason) {
        errors.declineReason = 'A reason is required';
      }
      if (values.declineReason === 'other' && !values.otherDeclineReason) {
        errors.otherDeclineReason = 'Please specify the reason';
      }
    } else if (step === 'confirm_approval') {
      if (String(values.confirmAction) !== 'true') {
        errors.confirmAction = 'You must confirm the action';
      }
    }
    return errors;
  };

  const currentModalProps: Record<ModalStepType, Partial<IModalProps>> = {
    request_approval: {
      heading: 'Approve access request to Issued cards',
      description: `Review detail below to approve request and enable access to ${cardCategory} cards for this merchant.`,
      size: 'md',
      maxHeight: '700px',
      isScrollable: true,
      headerBottomBorder: true,
      secondButtonActionIsTerminal: false,
      content: (
        <RequestingMerchantOverview
          pcidssLimitDetails={merchantPCIDSSLimitDetails}
          requestDetails={merchantRequestDetails}
          spendingLimitDetails={merchantSpendingLimitDetails}
          fundingLimitDetails={merchantFundingLimitDetails}
        />
      )
    },
    confirm_approval: {
      size: 'sm',
      secondButtonText: 'Yes, Confirm',
      firstButtonText: 'Back',
      completedDescription: `You have successfully approved and enabled this merchant's access to ${cardCategory} Cards.`,
      heading: 'Confirm Approval?',
      description: "Please confirm that you want to approve and enable this merchant's access to Issued Cards.",
      content: <ConfirmRequestApproval />,
      equalFooterBtn: true
    },
    request_decline: {
      size: 'sm',
      secondButtonText: 'Yes, Confirm',
      firstButtonText: 'Back',
      completedDescription: 'Merchant access request to Issued Cards has been declined.',
      secondButtonStyles: { backgroundColor: '#F32345' },
      heading: 'Confirm Decline?',
      description: `You are about to decline this merchant’s access request to ${cardCategory} Cards. Once declined, they will not be able to issue cards to their customers and will be required to submit a new request.`,
      modalBodyClassName: 'px-4',
      content: <DeclineRequestAccessForm />,
      equalFooterBtn: true
    }
  };

  if (step)
    return (
      <Formik
        initialValues={{
          declineReason: '',
          otherDeclineReason: '',
          confirmAction: false
        }}
        validate={validateForm}
        onSubmit={async formValues => {
          try {
            switch (step) {
              case 'request_decline':
                await mutateModifyAccess({
                  currency,
                  kora_id: Number(merchantData?.merchant_kora_id),
                  card_type: 'virtual',
                  access_type: 'card',
                  decline_request: true,
                  card_category: isCustomerCardCategory ? 'customer' : 'reserved',
                  access_request_reference: merchantData?.reference,
                  reason: formValues.otherDeclineReason || formValues.declineReason
                });
                break;
              case 'request_approval':
                setStep('confirm_approval');
                break;
              case 'confirm_approval':
                await mutateModifyAccess({
                  currency,
                  kora_id: Number(merchantData?.merchant_kora_id),
                  action: 'enable',
                  card_type: 'virtual',
                  access_type: 'card',
                  card_category: isCustomerCardCategory ? 'customer' : 'reserved',
                  access_request_reference: merchantData?.reference,
                  reason: 'Approve Merchant Access Request'
                });
                break;
              default:
                break;
            }
          } catch (error) {
            throw error;
          }
        }}
      >
        {props => (
          <Modal
            firstButtonAction={gotoPreviousStep}
            secondButtonAction={props.submitForm}
            secondButtonActionIsTerminal
            secondaryCompletedModal
            completedAction={() => history.push(`/dashboard/product-config/USD/card-issuance/${feature}?tab=requesting_merchants`)}
            {...currentModalProps[step]}
            secondButtonDisable={step === 'confirm_approval' && !props.values.confirmAction}
            close={onClose}
          />
        )}
      </Formik>
    );
};

export default AccessRequestReviewModal;

const DeclineRequestAccessForm = () => {
  const { values } = useFormikContext<FormValueType>();
  return (
    <div className="modal-content">
      <div className="">
        <label htmlFor="declineReason">Reason for declining </label>
        <Field as="select" className="form-control" name="declineReason">
          {declineReasonOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Field>
        <ErrorMessage name="declineReason" component="div" className="form-error mt-1" />
      </div>
      {values.declineReason === 'others' && (
        <div className="my-3">
          <label htmlFor="otherDeclineReason">Tell us why you want to decline this request</label>
          <Field
            as="textarea"
            id="otherDeclineReason"
            name="otherDeclineReason"
            rows={4}
            className="form-control"
            placeholder="Enter the specific reason..."
          />
          <ErrorMessage name="otherDeclineReason" component="div" className="form-error mt-1" />
        </div>
      )}
    </div>
  );
};
