import { useEffect, useState } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';

import { useFeedbackHandler, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import {
  availableProductType,
  CurrencyType,
  ModalType,
  productCategoriesType,
  updateProductConfigData,
  WithdrawalLimitType,
  WithdrawalLimitTypeKey
} from '+types';

import { getDefaultWithdrawalTab, getWithdrawalTabsForContext, withdrawalTypeTabs } from '../helpers/withdrawalLimitHelpers';

const apiRequest = new APIRequest();

const radioLabel = {
  default: 'for only merchants under the default configuration',
  all_merchants: 'for all merchants',
  custom_merchants: 'for only custom merchants'
} as const;

const useLimit = (type: 'product' | 'global' = 'global') => {
  const queryClient = useQueryClient();
  const searchQuery = useSearchQuery<{
    withdrawalLimitType: string;
    globalWithdrawalLimitType: string;
    product: productCategoriesType;
    currency: CurrencyType;
    productCategory: availableProductType;
  }>();
  const { feedbackInit, closeFeedback } = useFeedbackHandler();

  const { currency, merchant_id, product, feature } = useParams<{
    currency: CurrencyType;
    merchant_id: string;
    product: productCategoriesType;
    feature: availableProductType;
  }>();

  const category = searchQuery.value.product ?? product ?? 'pay-ins';
  const activeCurrency = searchQuery.value.currency ?? currency;
  const productCategory = searchQuery.value.productCategory ?? feature;
  const globalTab = searchQuery.value.globalWithdrawalLimitType as WithdrawalLimitTypeKey;
  const productTab = searchQuery.value.withdrawalLimitType as WithdrawalLimitTypeKey;

  const params = {
    account_id: merchant_id,
    currency: activeCurrency,
    payment_method: undefined
  };
  const { data: configData, refetch: refetchConfig } = useQuery(
    [`${activeCurrency}_PRODUCT_CONFIG`, merchant_id, activeCurrency, category, type],
    () => apiRequest.getProductConfiguration(activeCurrency, params),
    {
      refetchOnMount: 'always',
      cacheTime: 0,
      onError: () => {
        feedbackInit({
          message: `There has been an error in getting this ${merchant_id ? "merchant's" : ''} details`,
          type: 'danger',
          action: {
            action: () => refetchConfig(),
            name: 'Try again'
          }
        });
      }
    }
  );

  const [withdrawalLimitData, setWithdrawalLimitData] = useState<WithdrawalLimitType>();
  const [consent, setConsent] = useState(false);

  const computeWithdrawalLimitContent = () => {
    if (type === 'global') {
      return configData?.data?.setting?.transaction?.disbursement?.limits;
    }
    if (productCategory) {
      return {
        ...configData?.data?.setting?.transaction?.disbursement?.[productCategory]?.limits,
        limit: configData?.data?.setting?.transaction?.disbursement?.[productCategory]?.transaction_limit,
        channels: configData?.data?.setting?.transaction?.disbursement?.[productCategory]?.channels
      };
    }
    return undefined;
  };

  useEffect(() => {
    setWithdrawalLimitData(computeWithdrawalLimitContent() as WithdrawalLimitType);
    return () => setWithdrawalLimitData(undefined);
  }, [type, configData, searchQuery.value.productCategory, feature]);

  const resetWithdrawalLimitData = () => {
    setWithdrawalLimitData(computeWithdrawalLimitContent() as WithdrawalLimitType);
  };

  const currentMaxTransactionLimit = (withdrawalLimitData as WithdrawalLimitType)?.limit?.max as number;
  const currentMinTransactionLimit = (withdrawalLimitData as WithdrawalLimitType)?.limit?.min as number;

  const [modal, setModal] = useState<ModalType>(null);
  const [selected, setSelected] = useState<updateProductConfigData['type']>(undefined);

  const getActiveTabName = () => withdrawalTypeTabs.find(tab => tab.value === activeWithdrawalLimitType)?.label || '';

  const handleTabChange = (value: { value: string }) => {
    if (type === 'global') {
      searchQuery.setQuery({ globalWithdrawalLimitType: value.value });
    } else {
      searchQuery.setQuery({ withdrawalLimitType: value.value });
    }
  };

  const handleWithdrawalLimitChange = (data: WithdrawalLimitType) => {
    setWithdrawalLimitData(data);
  };

  const getActiveWithdrawalLimitType = () => {
    if (type === 'global') {
      return globalTab ?? 'web';
    } else {
      const defaultTab = getDefaultWithdrawalTab(withdrawalLimitData) as WithdrawalLimitTypeKey;

      if (productTab && withdrawalLimitData) {
        const availableTabs = getWithdrawalTabsForContext(type, withdrawalLimitData);
        const isTabAvailable = availableTabs.some(tab => tab.value === productTab);
        if (!isTabAvailable) {
          return defaultTab ?? 'web';
        }
      }

      return productTab ?? defaultTab ?? 'web';
    }
  };

  const activeWithdrawalLimitType = getActiveWithdrawalLimitType();

  useEffect(() => {
    if (type === 'global' && !searchQuery.value.globalWithdrawalLimitType) {
      searchQuery.setQuery({ globalWithdrawalLimitType: 'web' });
    } else if (type === 'product') {
      const currentTab = searchQuery.value.withdrawalLimitType as WithdrawalLimitTypeKey;

      if (!currentTab) {
        if (withdrawalLimitData) {
          const first = getDefaultWithdrawalTab(withdrawalLimitData);
          if (first) {
            searchQuery.setQuery({ withdrawalLimitType: first });
          }
        } else {
          searchQuery.setQuery({ withdrawalLimitType: 'web' });
        }
      } else if (withdrawalLimitData) {
        const availableTabs = getWithdrawalTabsForContext(type, withdrawalLimitData);
        const isCurrentTabAvailable = availableTabs.some(tab => tab.value === currentTab);

        if (!isCurrentTabAvailable) {
          const first = getDefaultWithdrawalTab(withdrawalLimitData);
          if (first) {
            searchQuery.setQuery({ withdrawalLimitType: first });
          }
        }
      }
    }
  }, [withdrawalLimitData, type, searchQuery.value.withdrawalLimitType, searchQuery.value.globalWithdrawalLimitType]);

  // Structure now: web.per_transaction.{settlement_account|non_settlement_account}.{min,max} and web.daily.{settlement_account, non_settlement_account}
  const settlementPerTxn = withdrawalLimitData?.web?.per_transaction?.settlement_account;
  const nonSettlementPerTxn = withdrawalLimitData?.web?.per_transaction?.non_settlement_account;
  const apiPerTxn = withdrawalLimitData?.api?.per_transaction;

  const settlementPerTxnMin = settlementPerTxn?.min ?? 0;
  const settlementPerTxnMax = settlementPerTxn?.max ?? 0;
  const nonSettlementPerTxnMin = nonSettlementPerTxn?.min ?? 0;
  const nonSettlementPerTxnMax = nonSettlementPerTxn?.max ?? 0;
  const apiPerTxnMin = apiPerTxn?.min ?? 0;
  const apiPerTxnMax = apiPerTxn?.max ?? 0;

  const greaterThanMaxTransactionLimit =
    activeWithdrawalLimitType === 'web'
      ? settlementPerTxnMax > currentMaxTransactionLimit || nonSettlementPerTxnMax > currentMaxTransactionLimit
      : apiPerTxnMax > currentMaxTransactionLimit;
  const lessThanMinTransactionLimit =
    activeWithdrawalLimitType === 'web'
      ? settlementPerTxnMin < currentMinTransactionLimit || nonSettlementPerTxnMin < currentMinTransactionLimit
      : apiPerTxnMin < currentMinTransactionLimit;

  const disableEditWithdrawalLimit = (greaterThanMaxTransactionLimit || lessThanMinTransactionLimit) && selected !== 'custom_merchants';

  // A per-transaction max must not exceed the corresponding daily aggregate limit (if daily limits exist)
  const settlementDaily = withdrawalLimitData?.web?.daily?.settlement_account ?? Infinity; // if no daily, don't block
  const nonSettlementDaily = withdrawalLimitData?.web?.daily?.non_settlement_account ?? Infinity;
  const disableEditWithdrawalLimitForTransactionMoreThanDailyLimit =
    activeWithdrawalLimitType === 'web' && (settlementPerTxnMax > settlementDaily || nonSettlementPerTxnMax > nonSettlementDaily);

  const anyValidationError = disableEditWithdrawalLimit || disableEditWithdrawalLimitForTransactionMoreThanDailyLimit;

  useEffect(() => {
    if (modal === 'limits') {
      if (disableEditWithdrawalLimit) {
        feedbackInit({
          message: greaterThanMaxTransactionLimit
            ? 'Withdrawal limit per transaction must be less than the max transaction limit'
            : 'Withdrawal limit per transaction must be more than the min transaction limit',
          componentLevel: true,
          type: 'warning',
          isClosable: false
        });
      } else if (disableEditWithdrawalLimitForTransactionMoreThanDailyLimit) {
        feedbackInit({
          message: 'Single transaction limit must be less than the daily limit',
          componentLevel: true,
          type: 'warning',
          isClosable: false
        });
      } else {
        closeFeedback();
      }
    }
  }, [modal, withdrawalLimitData, activeWithdrawalLimitType]);

  const getTabOptions = (content?: unknown) => {
    // For product context, use the full configuration data that includes bank_account info
    if (type === 'product' && productCategory && configData?.data?.setting?.transaction?.disbursement?.[productCategory]) {
      return getWithdrawalTabsForContext(type, configData.data.setting.transaction.disbursement[productCategory]);
    }
    return getWithdrawalTabsForContext(type, content);
  };

  return {
    getActiveTabName,
    handleTabChange,
    activeWithdrawalLimitType,
    handleWithdrawalLimitChange,
    anyValidationError,
    withdrawalLimitData,
    setWithdrawalLimitData,
    resetWithdrawalLimitData,
    radioLabel,
    selected,
    setSelected,
    modal,
    setModal,
    feedbackInit,
    closeFeedback,
    queryClient,
    consent,
    setConsent,
    activeCurrency,
    category,
    merchant_id,
    getTabOptions
  };
};

export default useLimit;
