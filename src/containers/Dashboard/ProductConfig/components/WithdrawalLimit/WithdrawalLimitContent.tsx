import React, { useEffect, useRef, useState } from 'react';

import { useReducerState } from '+hooks';
import {
  TransactionLimitType,
  updateProductConfigData,
  WebAccountType,
  WithdrawalChannelType,
  WithdrawalLimitType,
  WithdrawalLimitTypeKey
} from '+types';
import { backwardAmountInput, cleanInput, formatAmount, formatWithCommas } from '+utils';

import { defaultWithdrawalLimits, limitsLabel } from '../helpers/withdrawalLimitHelpers';

import './index.scss';

type limitsLabelType = keyof typeof limitsLabel;

const WithdrawalLimitContent = ({
  content,
  transactionLimit,
  handleWithdrawalLimitChange,
  merchantConfigType,
  selectedWithdrawalType
}: {
  content: WithdrawalLimitType;
  transactionLimit?: TransactionLimitType;
  handleWithdrawalLimitChange: (data: WithdrawalLimitType) => void;
  merchantConfigType?: updateProductConfigData['type'];
  selectedWithdrawalType: WithdrawalLimitTypeKey;
}) => {
  const defaultWithdrawalLimit = defaultWithdrawalLimits(content, merchantConfigType);
  const [withdrawalLimit, setWithdrawalLimit] = useReducerState({
    web: {
      daily: {
        settlement_account: defaultWithdrawalLimit.web.daily.settlement_account,
        non_settlement_account: defaultWithdrawalLimit.web.daily.non_settlement_account
      },
      settlement: {
        min: defaultWithdrawalLimit.web.settlement.min,
        max: defaultWithdrawalLimit.web.settlement.max
      },
      'non-settlement': {
        min: defaultWithdrawalLimit.web['non-settlement'].min,
        max: defaultWithdrawalLimit.web['non-settlement'].max
      }
    },
    api: {
      min: defaultWithdrawalLimit.api.min,
      max: defaultWithdrawalLimit.api.max
    },
    global: {
      daily: defaultWithdrawalLimit.global.daily
    }
  });

  // Track if we've already hydrated state from a fetched content object to avoid
  // overwriting user edits on subsequent parent state updates triggered by edits.
  const initializedFromContent = useRef<boolean>(false);

  const [isAnimating, setIsAnimating] = useState(false);
  const [animationKey, setAnimationKey] = useState(selectedWithdrawalType);
  const previousTabRef = useRef(selectedWithdrawalType);


  useEffect(() => {
    if (previousTabRef.current !== selectedWithdrawalType) {
      setIsAnimating(true);
      const exitTimer = setTimeout(() => {
        setAnimationKey(selectedWithdrawalType);
        previousTabRef.current = selectedWithdrawalType;
      }, 150);
      const enterTimer = setTimeout(() => {
        setIsAnimating(false);
      }, 300);

      return () => {
        clearTimeout(exitTimer);
        clearTimeout(enterTimer);
      };
    }
  }, [selectedWithdrawalType]);


  useEffect(() => {
    if (!initializedFromContent.current && content) {
      const freshDefaults = defaultWithdrawalLimits(content, merchantConfigType);
      setWithdrawalLimit({
        web: {
          daily: {
            settlement_account: freshDefaults.web.daily.settlement_account,
            non_settlement_account: freshDefaults.web.daily.non_settlement_account
          },
          settlement: {
            min: freshDefaults.web.settlement.min,
            max: freshDefaults.web.settlement.max
          },
          'non-settlement': {
            min: freshDefaults.web['non-settlement'].min,
            max: freshDefaults.web['non-settlement'].max
          }
        },
        api: {
          min: freshDefaults.api.min,
          max: freshDefaults.api.max
        },
        global: {
          daily: freshDefaults.global.daily
        }
      });
      initializedFromContent.current = true;
    }
  }, [content, merchantConfigType]);

  useEffect(() => {
    handleWithdrawalLimitChange({
      web: {
        daily: {
          settlement_account: +(withdrawalLimit?.web?.daily?.settlement_account || 0),
          non_settlement_account: +(withdrawalLimit?.web?.daily?.non_settlement_account || 0)
        },
        per_transaction: {
          settlement_account: {
            min: +(withdrawalLimit?.web?.settlement?.min || 0),
            max: +(withdrawalLimit?.web?.settlement?.max || 0)
          },
          non_settlement_account: {
            min: +(withdrawalLimit?.web?.['non-settlement']?.min || 0),
            max: +(withdrawalLimit?.web?.['non-settlement']?.max || 0)
          }
        }
      },
      api: {
        per_transaction: { min: +(withdrawalLimit?.api?.min || 0), max: +(withdrawalLimit?.api?.max || 0) }
      },
      global: {
        daily: +(withdrawalLimit?.global?.daily || 0)
      }
    });
  }, [withdrawalLimit]);

  type WebSectionType = 'daily' | WebAccountType;
  type GlobalSectionType = 'global';
  type FieldType = limitsLabelType | 'daily';
  const handleChange = (
    channel: WithdrawalChannelType,
    section: WebSectionType | 'api' | GlobalSectionType,
    field: FieldType,
    value: string
  ) => {
    const sanitized = value.replace(/,/g, '').replace(/[<>%$-]/gi, '');
    const formattedValue = backwardAmountInput(sanitized);
    const nextVal = String(formattedValue === '' ? 0 : formattedValue);
    const numericVal = Number(nextVal || 0);

    if (channel === 'api' && section === 'api') {
      setWithdrawalLimit({
        api: {
          ...withdrawalLimit.api,
          [field]: numericVal
        }
      });
    } else if (channel === 'global' && section === 'global') {
      setWithdrawalLimit({
        global: {
          ...withdrawalLimit.global,
          daily: numericVal
        }
      });
    } else if (section === 'daily') {
      setWithdrawalLimit({
        web: {
          ...withdrawalLimit.web,
          daily: {
            ...withdrawalLimit.web.daily,
            [field]: numericVal
          }
        }
      });
    } else if (section === 'settlement' || section === 'non-settlement') {
      setWithdrawalLimit({
        web: {
          ...withdrawalLimit.web,
          [section]: {
            ...withdrawalLimit.web[section],
            [field]: numericVal
          }
        }
      });
    }
  };

  return (
    <section className="withdrawal-limit-content">
      {transactionLimit && (
        <section className="withdrawal-limit-content__header">
          <div className="withdrawal-limit-content__header-info">
            <p>Withdrawal limits cannot be more than transaction limits</p>
          </div>
          <p>Min transaction limit: {formatAmount(transactionLimit?.min ?? 0)}</p>
          <p>Max transaction limit: {formatAmount(transactionLimit?.max ?? 0)}</p>
        </section>
      )}
      <section
        key={animationKey}
        className={`withdrawal-limit-content__body ${isAnimating ? 'withdrawal-limit-content__body--animating' : ''}`}
      >
        {Object.entries(withdrawalLimit[animationKey]).map(([values, keys]) => {
          return (
            <div className="withdrawal-limit-content__body-input" key={values}>
              {animationKey === 'global' && (
                <div className="currency-modal__content">
                  <div className="withdrawal-limit-content__body-input-item">
                    <span>Daily Limit</span>
                    <input
                      value={formatWithCommas(formatAmount(String(keys)))}
                      placeholder={`Enter value`}
                      type="numeric"
                      onChange={e => handleChange('global', 'global', 'daily', cleanInput(e.target.value))}
                    />
                  </div>
                </div>
              )}
              {animationKey === 'api' && (
                <div className="withdrawal-limit-content__body-input-item" key={values}>
                  <span>{limitsLabel[values as limitsLabelType]}</span>
                  <input
                    value={formatWithCommas(formatAmount(String(keys)))}
                    placeholder={`Enter ${values} ${values} value`}
                    type="numeric"
                    onChange={e => handleChange('api', 'api', values as limitsLabelType, cleanInput(e.target.value))}
                  />
                </div>
              )}
              {animationKey === 'web' && (
                <>
                  <span>{values}</span>
                  {Object.entries(keys as Record<string, string | number>).map(([value, key]) => {
                    return (
                      <div className="withdrawal-limit-content__body-input-item" key={value}>
                        <span>{limitsLabel[value as limitsLabelType]}</span>
                        <input
                          value={formatWithCommas(formatAmount(String(key)))}
                          placeholder={`Enter ${values} ${value} value`}
                          type="numeric"
                          onChange={e =>
                            handleChange('web', values as WebSectionType, value as limitsLabelType, cleanInput(e.target.value))
                          }
                        />
                      </div>
                    );
                  })}
                </>
              )}
            </div>
          );
        })}
      </section>
    </section>
  );
};

export default WithdrawalLimitContent;
