@import '+styles/kpy-custom/_custom';
@import '+styles/kpy-custom/variables';

.payins-container {
  display: flex;
  flex-direction: column;

  &__first {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 3rem;
    border-bottom: 1px solid #dde2ec;
    .controls {
      &.--option {
        display: flex;
        align-items: flex-end;
        column-gap: 0.8rem;
        flex-direction: row;

        p {
          margin-bottom: 0;
          font-weight: 500;
          color: #a9afbc;
        }

        .--enable-btn {
          background: #eaf2fe;
          border-color: #eaf2fe;
          color: #3e4b5b;
          font-weight: 500;
        }
        div:last-child {
          display: flex;
          width: 100%;
          align-items: flex-start;

          &.modal-footer {
            display: flex;
            flex-direction: row !important;
            align-items: center;
            justify-content: flex-start;
            div:last-child {
              display: flex;
              flex-direction: row;
            }
          }
        }
      }

      .ellipsis__nav {
        box-shadow: 0 2px 20px rgba(15, 24, 33, 0.09);
        padding: 0.5rem 0.8rem;
        top: 15rem;
        right: 3rem;
        .ellipsis__item .active {
          color: #2376f3;
          font-weight: 500;
        }
      }
    }

    div:first-child {
      font-family: 'Averta PE';
      & > span {
        display: flex;
        align-items: center;
        column-gap: 1rem;
        .status {
          max-width: 107px;
          padding: 2px 8px;
          border-radius: 0.2rem;
        }
      }

      p {
        margin-bottom: 0;
        font-weight: 500;
        color: #a9afbc;
        max-width: 500px;
      }
    }
    div:last-child {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      column-gap: 0.8rem;

      .--status {
        display: flex;
        align-items: center;
        column-gap: 0.5rem;

        p {
          margin-bottom: 0;
          font-weight: 500;
          color: #a9afbc;
        }

        .--enable-btn {
          background: #eaf2fe;
          border-color: #eaf2fe;
          color: #3e4b5b;
          font-weight: 500;
        }
      }

      .--settings {
        display: flex;
        align-items: center;
        column-gap: 0.3rem;
        margin-top: 1rem;
        cursor: pointer;

        p {
          margin-bottom: 0;
          font-weight: 500;
          color: #2376f3;
        }
      }
    }
  }
}
