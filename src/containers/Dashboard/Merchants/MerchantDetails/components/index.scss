@import '../../../../../styles/kpy-custom/variables';

.custom-pill-value {
  font-size: 0.7rem !important;
  margin-left: 0.5rem;
}

.custom-action-list {
  right: unset !important;
}

.custom--checkbox {
  margin: 0 1rem;
  width: 30px !important;
}

.loading-primary-btn {
  padding: 0rem 3rem;
}

.form-control {
  height: 40px;
}

.infobutton {
  border: none;
  background-color: inherit;
}

.custom-fade {
  opacity: 0.7;
  margin: 0.5rem 0rem;
  padding: 0rem 0.5rem;
}

.custom-btn-success {
  color: #24b314;
  background-color: #fff;
  border-color: #24b314;
  &:hover {
    background-color: #24b314 !important;
    color: #fff !important;
  }
}

.custom--close {
  font-weight: 500;
  font-size: 20px;
  color: red;
  cursor: pointer;
}

.custom--minimize {
  font-weight: 500;
  font-size: 20px;
  color: #007bff;
  cursor: pointer;
}

.fee--header {
  font-size: 0.9rem;
}

.fee--content {
  font-size: 13px;
  opacity: 0.7;
  text-transform: capitalize;
}

.custom--legend {
  margin: 1rem 0;
}

.custom--link {
  border: unset;
  &hover {
    border: unset;
  }
}

/* Merchant Wallet */
.wallet-box {
  background-color: rgba(86, 140, 210, 0.078);
  padding: 1.2rem;
  border-radius: 10px 10px;
  margin-bottom: 1rem;
}

.wallet-box.lien-available {
  border-radius: 10px 10px 0 0;
  margin-bottom: 0;
}

.wallet-box.no-lien {
  border-radius: 10px;
  margin-bottom: 1rem;
}

.wallet-heading {
  font-weight: 500;
  opacity: 0.35;
}

.wallet-box.lien-available .wallet-details {
  border-bottom: 1px solid #dde2ec;
}
.wallet-box.no-lien .wallet-details {
  border-bottom: none;
}

.wallet-box .wallet-details {
  display: flex;
  padding: 0.5rem;
  flex-direction: column;
  gap: 1.5rem;

  > * {
    flex-basis: 33.33%;
  }

  @media (min-width: $breakpoint-tablet) {
    flex-direction: row;
  }

  .--loading {
    width: 90%;
    display: flex;
    flex-direction: row;
  }
}

.wallet-box .wallet-details .wallet-data {
  color: #3b618e;
  padding-bottom: 2rem;

  @media (min-width: $breakpoint-tablet) {
    margin-right: 1rem;
    padding-bottom: 0;
  }
}

.wallet-box .wallet-details .wallet-data .wd-label {
  font-size: 0.7rem;
  margin-bottom: 0;
  text-transform: uppercase;
  opacity: 0.4;
  letter-spacing: 1px;
}

.wallet-box .wallet-details .wallet-data .wd-value {
  font-size: 2rem;
  font-weight: 500;
  margin-bottom: 0;
}

.wallet-box .lien-container {
  padding: 1rem 1rem 0 0;

  & > span {
    font-size: 0.8rem;
    color: #2376f3;
    font-weight: 500;
    cursor: pointer;
  }
}

.lien-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff8e1;
  padding: 0.8rem 1.8rem;
  border-radius: 0 0 10px 10px;

  &-left {
    display: flex;
    align-items: center;
    gap: 0.3rem;

    svg {
      height: 15px;
      width: 15px;
    }
    p {
      color: #414f5f;
      font-size: 0.7rem;
      font-weight: 500;
      margin-bottom: 0;
    }
  }
  &-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #2376f3;
    p {
      color: #915200;
      font-size: 0.7rem;
      font-weight: 500;
      margin-bottom: 0;
    }
    span {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: #c10b28;
      display: flex;
      align-items: center;
      justify-content: center;

      p {
        color: #ffffff;
        font-size: 0.7rem;
        font-weight: 500;
        margin-bottom: 0;
      }
    }
  }
}
.lien-container span:not(:last-child)::after {
  content: '|';
  margin-left: 10px;
  margin-right: 10px;
  color: #a9afbc;
}
.rba-container {
  @media (min-width: 600px) {
    flex-direction: row;
  }
}

// Fees Component
.merchants-fees {
  &__payouts {
    margin-bottom: 0;
  }

  &__currency-toggle {
    margin: 1rem 0 2rem;
  }
}

.rule-container {
  .fee-item {
    margin-bottom: 1.25rem;

    > div:first-child p:first-child {
      margin-bottom: 1rem;
    }

    @media screen and (min-width: 600px) {
      align-items: flex-end;
    }
  }
}

.rule-box {
  margin-bottom: 0.25rem;
  background: #ffffff;
  box-shadow: 0px 3px 5px rgba(126, 142, 177, 0.1);
  border-radius: 10px;

  .rule-heading {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: 300;
    font-size: 0.95rem;
    padding: 0.8rem 1.2rem;
    background-color: rgba(231, 234, 242, 0.75);
    border-radius: 5px 5px 0 0;

    @media screen and (min-width: 600px) {
      flex-direction: row;
    }

    p {
      margin: 0;
      align-self: center;
    }
  }

  .rule-details {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    padding: 0.8rem 1.2rem;
    align-items: flex-end;
    gap: 1rem;

    p {
      margin-bottom: 0;
      line-height: 30px;
      font-size: 0.92rem;
    }

    .--detail-green,
    .--detail-grey {
      font-weight: 500;
      letter-spacing: 0.0863636px;
      padding: 3px 6px;
      border-radius: 0.5rem;
    }

    .--detail-green {
      color: #24b314;
      background: rgba(36, 179, 20, 0.1);
    }

    .--detail-grey {
      color: #636c72;
      background: rgba(99, 108, 114, 0.15);
    }

    @media screen and (min-width: 600px) {
      flex-direction: row;
      align-items: center;
    }

    .rule-data {
      padding-right: 2rem;
      color: #3b618e;

      @media screen and (max-width: 600px) {
        margin-bottom: 1rem;
      }

      .rd-label {
        font-size: 0.63rem;
        text-transform: uppercase;
        opacity: 0.4;
        letter-spacing: 1px;
      }

      .rd-value {
        font-size: 1rem;
        font-weight: 500;
      }
    }
  }
}

.sibling {
  display: flex;
  justify-content: space-between;

  .form-group {
    width: 48%;
  }
}

.fees-heading-buttons {
  height: max-content;
}

.fees-heading-buttons-w {
  display: flex;

  button.btn--link {
    color: #2376f3;
    font-weight: 500;
  }
}

.fee--custom-indicator {
  position: relative;
  margin-left: auto;

  .custom--indicator {
    display: inline-block;
    height: 0.625rem;
    width: 0.625rem;
    background-color: #ff9247;
    border-radius: 50%;
    position: absolute;
    left: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
  }
}

.fees-custom--text {
  > div {
    margin-top: 0.5rem;
    margin-bottom: 2rem;
  }

  img {
    width: 14px;
  }

  p {
    font-size: 11px;
    color: #ff9247;
    line-height: 15px;
  }

  @media (min-width: 600px) {
    max-width: 185px;

    > div {
      margin-top: 1rem;
      margin-bottom: 0;
    }
  }
}

.sub-info-detail {
  padding-top: 0.25rem;
  color: rgba(62, 75, 91, 0.7);
}

// Business Information Component

.business__info {
  .businnes-content-details-body {
    background-color: #f1f6fa;
    margin-top: 10px;
    border-radius: 5px;
    padding: 1rem;

    @media (min-width: 768px) {
      padding-top: 1.5rem;
      padding-left: 2rem;
    }
  }

  .detail-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 1.5rem;

    @media (min-width: 768px) {
      flex-direction: row;
      align-items: stretch;
      gap: 1rem;
    }
  }

  .overview_tier {
    display: flex;
    padding: 10px 0;
    margin-top: 20px;
  }

  .overview_invoice_body {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
    gap: 20px;
    width: 100%;

    @media (min-width: 768px) {
      width: 25%;
    }
  }

  .overview_invoice_body-about {
    display: flex;
    padding: 10px 0;
    flex-wrap: wrap;
    gap: 20px;
    width: 100%;

    @media (min-width: 768px) {
      width: 35%;
    }
  }

  .vertical-divider {
    display: none;

    @media (min-width: 768px) {
      display: block;
    }
  }

  .invoice-desc {
    .desc-label {
      font-size: 15px;
      font-weight: 500;
    }
    .desc-value {
      word-break: break-all;
      text-align: justify;
      color: #aeb3bf;
    }
  }

  .invoice-desc-overview {
    padding-left: 0;
    margin-bottom: 1rem;

    @media (min-width: 768px) {
      border-left: 1px solid #dee2e6;
      padding-left: 20px;
      margin-bottom: 0;
    }

    .desc-label {
      font-size: 15px;
      font-weight: 500;
    }
    .desc-value {
      word-break: break-all;
      text-align: justify;
      color: #aeb3bf;
    }
    .desc-value-country {
      color: #aeb3bf;
      display: flex;
      align-items: center;
      gap: 5px;
      svg {
        height: 20px;
        width: 20px;
      }
    }
  }

  .invoice-desc-overview:not(:last-child) {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;

    @media (min-width: 768px) {
      border-bottom: none;
      padding-bottom: 0;
    }
  }

  .merchant-details-g-menu {
    height: auto;
    @media (min-width: 768px) {
      height: 50px;
    }

    .transaction-duration-select {
      width: 100%;

      @media (min-width: 768px) {
        width: 40%;
      }
    }
  }
  .business-settings__currency-switch {
    margin-top: 10px;

    @media (min-width: 600px) {
      margin-top: -25px;
    }
  }
}

// Rolling reserve
.rolling-reserve {
  p.rolling-reserve__updated {
    font-size: 0.75rem;
    color: #aeb3bf;
    font-weight: 300;
    flex-grow: 1;
    text-align: right;
  }

  @media (min-width: $breakpoint-desktop) {
    &__desc {
      max-width: 60%;
    }
  }
}

// Balances
.balances__history {
  &-tabs {
    flex-direction: column-reverse;
  }

  &-export-button {
    padding: 0.6rem 0;

    > div {
      float: right;
    }
  }

  @media (min-width: $breakpoint-desktop-sm) {
    &-tabs {
      flex-direction: row;
      align-items: center;
    }

    &-export-button {
      padding: 0.6rem 0;
      border-bottom: 1px solid #dee2e6;

      > div {
        float: none;
      }
    }
  }
}

.balances__history-export-button {
  padding: 0.47rem 0;
}

.payment-preferences {
  &__heading {
    width: 100%;
  }

  &__heading-sub {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-bottom: 1.5rem;
  }

  &__risk-level {
    display: inline-block;
    margin-left: 0.4rem;
  }

  &__section {
    margin-bottom: 1.5rem;
    border: none;
  }

  &__config {
    font-weight: 500;
    color: #414f5f;
  }

  &__dest-w {
    h6 {
      display: flex;
      align-items: center;
    }

    h6 > button {
      border: none;
      background: none;
      cursor: pointer;
      padding: 0;
      margin-top: -3px;
    }

    h6 > button > img {
      width: 1rem;
    }
  }

  &__modal-content article > p {
    color: rgba(62, 75, 91, 0.7);
  }

  &__last-updated {
    display: flex;
    gap: 0.5rem;
    color: #a9afbc;
  }

  &__pref {
    min-width: 250px;
  }

  @media (min-width: $breakpoint-desktop-sm) {
    &__heading-sub {
      flex-direction: row;
      align-items: start;
      justify-content: space-between;
      gap: 1.5rem;
      margin-bottom: 0;
    }
  }

  @media (min-width: $breakpoint-desktop) {
    &__heading {
      width: 60%;
    }
  }
}

// email configuration
.content-wrapper {
  border-bottom: 1px solid #eaf2fe;

  .email-default {
    max-width: 30.15rem;
  }
}
.email-categories {
  ul {
    padding-inline-start: 0;
    li {
      color: #414f5f;
      font-size: 14px;
      padding: 12px 15px;
      max-width: 300px;
      list-style-type: none;
    }

    .active {
      color: #2376f3;
      background-color: #f1f6fa;
      border-radius: 8px;
      font-weight: 500;
    }

    li:hover {
      cursor: pointer;
      color: #2376f3;
      font-weight: 500;
    }
  }
}

.configuration-form {
  padding-left: 30px;
  h3 {
    color: #414f5f;
    font-size: 18px;
  }

  .description {
    color: #94a7b7;
    font-size: 16px;
    margin-top: 15px;
  }

  .sub-text {
    color: #414f5f;
    font-size: 15px;
    margin-top: 20px;
    padding: 0;
  }

  .checkbox-text {
    font-size: 15px;
    color: #414f5f;
  }

  .form-check-input {
    height: 15px;
    width: 15px;
  }

  .configuration-form-heading {
    border-bottom: 1px solid #eaf2fe;
    max-width: 44.5rem;

    .email-input {
      &:disabled {
        background-color: #fff !important;
      }
    }
  }
}

.category-heading {
  color: #a9afbc;
  text-transform: uppercase;
  padding-left: 0.94rem;
}

.form-desc-text {
  max-width: 30.05rem;
  font-size: 1rem;
  line-height: normal;
  letter-spacing: -0.009rem;
}

.subscribe-box {
  width: 100%;
  display: flex;
  justify-content: center;

  .subscribe-box-input {
    width: 100%;
    margin-right: 10px;
  }
}

.sub-desc {
  max-width: 44.5rem;
  display: flex;
  justify-content: space-between;
  margin-left: 0;

  .sub-desc-text {
    color: #414f5f;
    font-size: 1rem;
    margin-left: 0;
  }

  .sub-interval {
    box-shadow: none;
    color: #414f5f;
    cursor: pointer;
    font-weight: 500;
  }
}

//Teams

.member-name {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0 !important;
  .lock {
    width: 15px;
    height: 15px;
  }
}
.last-login {
  display: flex !important;
  align-items: center;
  gap: 2rem;
  justify-content: space-between;
  width: 100%;
  max-width: 200px !important;
  .option-container {
    padding: 0 !important;
    .ellipsis {
      padding: 0.5rem 0;
      width: 32px;
    }

    .option-list {
      width: 145px;
      background: #000000;
      position: absolute;
      top: 210px !important;
      left: 250px;
      border-radius: 5px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 0.8rem;

      @media (min-width: $breakpoint-desktop) {
        top: 30px !important;
        left: 90%;
      }

      .option-item {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 15px;
          height: 15px;
        }

        svg {
          margin-right: 10px;
          width: 15px;
          height: 15px;
        }
        .option-text {
          font-size: 0.75rem;
          color: #ffffff;
          font-weight: 400;
        }
      }
    }
  }
}

.unlock-bold {
  font-weight: 600;
}

.merchant-details-g-menu {
  height: 50px;
  margin-bottom: 20px;

  @media (max-width: $breakpoint-mobile) {
    height: auto;
    display: flex;
    justify-content: flex-end;

    .transaction-duration-select {
      width: 40%;
    }
  }
}
//Roles

.teams {
  padding: 1.5rem 0rem;
  & .form-desc {
    max-width: 600px;
  }

  &__cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  &__details {
    border: none;
    background: initial;
    color: #2376f3;
    font-weight: 500;
    cursor: pointer;
    &:active,
    &:focus {
      border: none;
    }
  }
}

.team-permission-input {
  height: 1rem !important;
  border: solid 0.14rem #dde2ec !important;
  padding: 1rem !important;
  box-shadow: none !important;
  width: 100% !important;
}

.team-permission-desc {
  margin-top: 0.94rem !important;
}

.--roles-table {
  &.div-table {
    &.--row {
      div {
        @media only screen and (min-width: $breakpoint-desktop) {
          padding: 0.4rem 1.3rem;
        }
      }
    }
  }
}

.tier-form-desc {
  max-width: 30rem;
  margin-bottom: 0;
  border: 0;
}

.tier-tooltip-text {
  text-transform: capitalize;
}
.tier-tooltip-text-questionmark {
  margin-top: -6px;
}
.ip-form-desc-text {
  max-width: 40.05rem;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: -0.009rem;
  color: #aeb3bf;
  font-weight: 400;
}

.merchant-details-unverified-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #fff8e1;
  padding: 5px;
  border-radius: 20px;
  padding: 5px 10px;
}
.merchant-details-unverified-banner span {
  font-size: 0.8rem;
  color: #915200;
}
.merchant-details-verified-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #e6f7ee;
  padding: 5px 10px;
  border-radius: 20px;
}

.merchant-details-verified-banner span {
  font-size: 0.8rem;
  color: #24b314;
}
.merchant-details-value-pair {
  padding: 0px !important;
  margin: 0px !important;
}

.merchant-details-activation-button {
  background-color: #f1f6fa;
  padding: 7px 20px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 40px;

  &.manage {
    color: #037cf8;
  }
}
