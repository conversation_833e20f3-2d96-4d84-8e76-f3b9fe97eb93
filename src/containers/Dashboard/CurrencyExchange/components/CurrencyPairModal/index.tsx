import React, { useEffect, useRef, useState } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { TIconNames } from '+containers/Dashboard/Shared/Icons/IconNames';
import LoadingPlaceholder from '+containers/Dashboard/Shared/LoadingPlaceHolder';
import { useFeedbackHandler } from '+hooks';
import Modal from '+shared/Modal';
import { ICurrencyPairActionModal } from '+types/conversions';
import { capitalizeFirst, getDate, getTime } from '+utils';

import { useUpdateCurrencyPairStatus, useUpdateMarkUp } from '../currencyExchangeHelper';

import '../index.scss';

const CurrencyPairModal = ({ action, close, currencyPair, isMarkupLoading }: ICurrencyPairActionModal) => {
  const { feedbackInit } = useFeedbackHandler();
  const { mutateAsync: updateCurrencyPairStatus } = useUpdateCurrencyPairStatus(feedbackInit);
  const { mutateAsync: updateCurrencyPairMarkup } = useUpdateMarkUp(feedbackInit);

  const [stage, setStage] = useState<'edit' | 'confirm' | 'disable' | 'enable'>(action);

  const [markup, setMarkup] = useState(currencyPair?.base_markup ?? '');

  const markupInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (stage === 'edit') {
      setTimeout(() => {
        markupInputRef.current?.focus();
      }, 0);
    }
  }, [stage, currencyPair]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value.replace(/\s/g, '');
    setMarkup(inputValue);
  };

  const getStatusModalOptions = (action: 'disable' | 'enable') => ({
    size: 'md',
    heading: `${capitalizeFirst(action)} Currency Pair?`,
    description: (
      <span>
        You&apos;re about to {action.toLowerCase()} the currency pair:{' '}
        <span className="font-weight-bold">
          {currencyPair?.from_currency} - {currencyPair?.to_currency}
        </span>
        .{' '}
        {action !== 'enable'
          ? 'This action will prevent users from initiating new conversions with this pair'
          : 'This currency pair would now be available for cnversions'}
        .
      </span>
    ),
    content: <></>,
    secondaryCompletedModal: true,
    secondButtonText: `Yes, ${capitalizeFirst(action)}`,
    completedHeading: `${capitalizeFirst(action)}d Currency Pair`,
    completedDescription: `The selected currency pair has been successfully ${action.toLowerCase()}d.`,
    completedActionText: 'Dismiss',
    secondButtonStyles: { backgroundColor: action === 'disable' ? 'hsla(350, 90%, 55%, 1)' : 'hsla(114, 80%, 39%, 1)' },
    secondButtonDisable: false,
    secondButtonAction: async () =>
      await updateCurrencyPairStatus({
        to_currency: currencyPair?.to_currency || '',
        from_currency: currencyPair?.from_currency || '',
        enable: action === 'enable'
      })
  });

  const modalOptions = {
    shared: {
      close: () => {
        close();
      }
    },
    disable: getStatusModalOptions('disable'),
    enable: getStatusModalOptions('enable'),
    confirm: {
      size: 'md',
      heading: <p>Save Changes?</p>,
      content: (
        <div className="currency-container p-3">
          <div className="d-flex justify-content-between mb-3">
            <span className="font-weight-bold annotation">Currency Pair</span>
            <span className="font-weight-bold">
              {currencyPair?.from_currency} - {currencyPair?.to_currency}
            </span>
          </div>
          <div className="d-flex justify-content-between">
            <span className="font-weight-bold annotation">New Markup</span>
            <span className="font-weight-bold">{markup}%</span>
          </div>
        </div>
      ),
      description: <p className="font-weight-bold">Please confirm that you want to save the changes made to this currency pair</p>,
      secondButtonText: 'Yes, Save',
      secondButtonTestId: 'markup-edit-save',
      secondaryCompletedModal: true,
      completedDescription: 'The rate configuration has been saved.',
      completedActionText: 'Dismiss',
      secondButtonAction: async () => {
        await updateCurrencyPairMarkup({
          to_currency: currencyPair?.to_currency,
          from_currency: currencyPair?.from_currency,
          base_markup: Number(markup)
        });
      }
    },
    edit: {
      size: 'md',
      heading: 'Edit Currency Pair',
      description:
        'Define how one currency converts into another. Each pair determines the conversion direction, limits, exchange rates, and markup settings.',
      firstButtonText: 'Cancel',
      secondButtonText: 'Continue',
      secondButtonTestId: 'markup-edit-continue',
      secondaryCompletedModal: false,
      secondButtonDisable: markup === '' || Number(markup) < 0 || Number(markup) === currencyPair?.base_markup,
      secondButtonActionIsTerminal: false,
      secondButtonAction: () => {
        setStage('confirm');
      },
      content: (
        <>
          <div className="d-flex align-items-end gap-xs">
            <section className="flex-grow-1">
              <div className="element-box-tp">
                <section className="d-flex flex-column">
                  <span className="semibold">Base Currency</span>
                  <span className="flex-grow currency-container border text-muted">{currencyPair?.from_currency}</span>
                </section>
              </div>
            </section>
            <div className="currency-container icon">
              <Icon name="arrowRight" width={20} height={20} strokeWidth={1} stroke="#000" className="m-0" />
            </div>
            <div className="flex-grow-1">
              <div className="element-box-tp">
                <div className="d-flex flex-column">
                  <span className="semibold">Target Currency</span>
                  <span className="flex-grow currency-container border text-muted">{currencyPair?.to_currency}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="form-group">
              <label htmlFor="markup" className="semibold">
                Markup
              </label>
              <input
                type="number"
                id="markup"
                name="markup"
                ref={markupInputRef}
                min={0}
                className="form-control"
                placeholder={` ${currencyPair?.from_currency}/${currencyPair?.to_currency}`}
                value={markup}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="currency-pair-detail-container ">
            <div className="d-flex border-bottom justify-content-between mb-3 py-3 ">
              <h6 className="cp-header">Pair Summary</h6>
              <div className="d-flex align-items-center p-0">
                <Icon name={currencyPair?.icon as TIconNames} width={13} height={13} stroke={currencyPair?.icon_color} />
                <span className={`status ${currencyPair?.status} ml-2 font-weight-bold`}>{capitalizeFirst(currencyPair?.status)}</span>
              </div>
            </div>

            <div className="d-flex justify-content-between">
              <p className="font-weight-bold title">Provider Rate</p>
              {isMarkupLoading && !currencyPair?.provider_rate ? (
                <LoadingPlaceholder type="text" content={1} rows={1} textWidth={150} />
              ) : (
                <p className="font-weight-bold">
                  1.00 {currencyPair?.from_currency} = {currencyPair?.provider_rate} {currencyPair?.to_currency}
                </p>
              )}
            </div>
            <div className="d-flex justify-content-between">
              <p className="font-weight-bold title">Kora&apos;s Rate</p>
              {isMarkupLoading && !currencyPair?.kora_rate ? (
                <LoadingPlaceholder type="text" content={1} rows={1} textWidth={150} />
              ) : (
                <p className="font-weight-bold">
                  1.00 {currencyPair?.from_currency} = {currencyPair?.kora_rate} {currencyPair?.to_currency}
                </p>
              )}
            </div>
            <div className="d-flex justify-content-between">
              <p className="font-weight-bold title">Last Set</p>
              <p className="font-weight-bold">{`${getDate(currencyPair?.last_updated)} ${getTime(currencyPair?.last_updated)}`}</p>
            </div>
          </div>
        </>
      )
    }
  };
  const modalProps = {
    ...modalOptions.shared,
    ...modalOptions[stage]
  };
  // eslint-disable-next-line react/jsx-props-no-spreading
  return <Modal {...modalProps} />;
};
export default CurrencyPairModal;
